import React, { useState } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Checkbox } from "@/components/ui/checkbox";
import { Printer } from "lucide-react";

interface ContractData {
  employeeName: string;
  employeeAddress: string;
  employeeBirthDate: string;
  startDate: string;
  employmentType: 'permanent' | 'temporary';
  temporaryUntil: string;
  temporaryReason: string;
  hasProbation: boolean;
  probationMonths: string;
  jobTitle: string;
  accountNumber: string;
}

const ContractGenerator = () => {
  const [contractData, setContractData] = useState<ContractData>({
    employeeName: '',
    employeeAddress: '',
    employeeBirthDate: '',
    startDate: '',
    employmentType: 'permanent',
    temporaryUntil: '',
    temporaryReason: '',
    hasProbation: false,
    probationMonths: '',
    jobTitle: '',
    accountNumber: ''
  });

  const [showContract, setShowContract] = useState(false);

  const handleInputChange = (field: keyof ContractData, value: string | boolean) => {
    setContractData(prev => ({ ...prev, [field]: value }));
  };

  const generateContract = () => {
    setShowContract(true);
  };

  const handlePrint = () => {
    window.print();
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return '__.__.__';
    const date = new Date(dateString);
    return date.toLocaleDateString('no-NO');
  };

  const contractContent = `
<div class="contract-document">
  <h1 class="contract-title">ARBEIDSAVTALE</h1>
  
  <div class="parties-section">
    <div class="party-block">
      <strong>Arbeidsgiver</strong><br/>
      Ringerike Landskap AS<br/>
      Org.nr 924 826 541<br/>
      Birchs vei 7, 3530 Røyse
    </div>
    
    <div class="party-block">
      <strong>Arbeidstaker</strong><br/>
      Navn: ${contractData.employeeName || '_'.repeat(28)}<br/>
      Adresse: ${contractData.employeeAddress || '_'.repeat(25)}<br/>
      Fødselsdato: ${contractData.employeeBirthDate || '_'.repeat(21)}
    </div>
  </div>

  <hr class="section-divider"/>

  <div class="contract-section">
    <h2>1 Ansettelsesforhold</h2>
    <ul class="contract-list">
      <li><strong>Startdato:</strong> ${formatDate(contractData.startDate) || '_'.repeat(19)}</li>
      <li><strong>Ansettelsestype:</strong> ${contractData.employmentType === 'permanent' ? '☑ Fast ☐ Midlertidig' : '☐ Fast ☑ Midlertidig'}${contractData.employmentType === 'temporary' ? ` t.o.m. ${formatDate(contractData.temporaryUntil) || '_'.repeat(12)}` : ''}</li>
      ${contractData.employmentType === 'temporary' ? `<li><em>Grunnlag (hvis midlertidig): ${contractData.temporaryReason || '_'.repeat(33)}</em></li>` : ''}
      <li><strong>Prøvetid:</strong> ${contractData.hasProbation ? `☑ ${contractData.probationMonths || '___'} mnd` : '☑ Ingen'} (maks 6)</li>
      ${contractData.hasProbation ? '<li><em>Gjensidig oppsigelsesfrist i prøvetid: 14 dager</em></li>' : ''}
    </ul>
  </div>

  <hr class="section-divider"/>

  <div class="contract-section">
    <h2>2 Arbeidssted</h2>
    <p>Prosjektbasert innen Ringerike og omegn; oppmøtested avtales for hvert prosjekt. Hvis variabelt arbeidssted, gjelder dette som hovedregel.</p>
  </div>

  <hr class="section-divider"/>

  <div class="contract-section">
    <h2>3 Stilling & oppgaver</h2>
    <p><strong>Stilling:</strong> ${contractData.jobTitle || '_'.repeat(31)}<br/>
    Arbeid innen anleggsgartner‑ og grunnarbeid samt annet arbeid naturlig knyttet til virksomheten.</p>
  </div>

  <hr class="section-divider"/>

  <div class="contract-section">
    <h2>4 Arbeidstid & pauser</h2>
    <ul class="contract-list">
      <li>Ordinær tid: <strong>37,5 t per uke</strong>, normalt kl. 07:00 – 15:00.</li>
      <li>Minst én 30 min pause ved arbeidsdag over 5,5 t.</li>
      <li>Overtid honoreres med <strong>≥ 40 %</strong> tillegg etter AML § 10‑6.</li>
    </ul>
  </div>

  <hr class="section-divider"/>

  <div class="contract-section">
    <h2>5 Lønn & godtgjørelse</h2>
    <table class="compensation-table">
      <tr>
        <td><strong>Element</strong></td>
        <td><strong>Sats / tidspunkt</strong></td>
      </tr>
      <tr>
        <td>Timesats</td>
        <td><strong>kr 300,-</strong></td>
      </tr>
      <tr>
        <td>Utbetaling</td>
        <td>5. hver måned til konto nr.: ${contractData.accountNumber || '_'.repeat(10)}</td>
      </tr>
      <tr>
        <td>Overtidstillegg</td>
        <td>≥ 40 % av timelønn</td>
      </tr>
      <tr>
        <td>Kjøring egen bil</td>
        <td>Statens trekkfrie sats – pt. <strong>3,50 kr/km</strong></td>
      </tr>
      <tr>
        <td>Pensjon</td>
        <td>OTP ‑ minimum 2 % av lønn</td>
      </tr>
    </table>
  </div>

  <hr class="section-divider"/>

  <div class="contract-section">
    <h2>6 Ferie & feriepenger</h2>
    <ul class="contract-list">
      <li><strong>5 uker ferie</strong> pr. år (Ferieloven).</li>
      <li>Feriepenger <strong>12 %</strong>; utbetales før hovedferie / ved fratreden når aktuelt.</li>
    </ul>
  </div>

  <hr class="section-divider"/>

  <div class="contract-section">
    <h2>7 Oppsigelse</h2>
    <table class="termination-table">
      <tr>
        <td><strong>Situasjon</strong></td>
        <td><strong>Frist</strong></td>
      </tr>
      <tr>
        <td>I prøvetid</td>
        <td>14 dager</td>
      </tr>
      <tr>
        <td>Etter prøvetid</td>
        <td>1 måned gjensidig</td>
      </tr>
    </table>
    <p class="legal-note">Oppsigelse skal skje skriftlig iht. AML kap. 15.</p>
  </div>

  <hr class="section-divider"/>

  <div class="contract-section">
    <h2>8 Diverse vilkår</h2>
    <ul class="contract-list">
      <li>Arbeidstaker følger instruks, HMS‑rutiner og bruk av verneutstyr.</li>
      <li>Arbeidsgiver stiller nødvendig arbeidstøy og verktøy.</li>
      <li>Ingen tariffavtale er gjeldende pr. dags dato.</li>
      <li>Endringer i arbeidsforholdet dokumenteres skriftlig som vedlegg til denne avtalen.</li>
    </ul>
  </div>

  <hr class="section-divider"/>

  <div class="signature-section">
    <h2>Signaturer</h2>
    <table class="signature-table">
      <tr>
        <td><strong>Sted / dato:</strong> Røyse, ${formatDate(contractData.startDate) || '_'.repeat(14)}</td>
        <td></td>
      </tr>
      <tr>
        <td><strong>Arbeidsgiver:</strong> _________________________________</td>
        <td><strong>Arbeidstaker:</strong> _________________________________</td>
      </tr>
    </table>
  </div>

  <hr class="section-divider"/>

  <div class="footer-note">
    <em>Avtalen er inngått i to eksemplarer; hver part beholder ett.</em>
  </div>
</div>

<style>
  .contract-document {
    font-family: 'Times New Roman', serif;
    line-height: 1.4;
    color: #000;
    max-width: 210mm;
    margin: 0 auto;
    padding: 20mm;
    background: white;
    font-size: 11pt;
  }
  
  .contract-title {
    text-align: center;
    font-size: 16pt;
    font-weight: bold;
    margin-bottom: 20px;
    text-transform: uppercase;
    letter-spacing: 1px;
  }
  
  .parties-section {
    display: flex;
    justify-content: space-between;
    margin-bottom: 15px;
  }
  
  .party-block {
    flex: 1;
    margin-right: 20px;
  }
  
  .party-block:last-child {
    margin-right: 0;
  }
  
  .section-divider {
    border: none;
    border-top: 1px solid #000;
    margin: 12px 0;
  }
  
  .contract-section {
    margin-bottom: 12px;
  }
  
  .contract-section h2 {
    font-size: 12pt;
    font-weight: bold;
    margin-bottom: 6px;
    color: #000;
  }
  
  .contract-list {
    margin: 0;
    padding-left: 20px;
    list-style-type: disc;
  }
  
  .contract-list li {
    margin-bottom: 2px;
  }
  
  .compensation-table, .termination-table, .signature-table {
    width: 100%;
    border-collapse: collapse;
    margin: 8px 0;
  }
  
  .compensation-table td, .termination-table td {
    border: 1px solid #000;
    padding: 4px 8px;
    font-size: 10pt;
  }
  
  .signature-table td {
    padding: 8px;
    vertical-align: bottom;
  }
  
  .legal-note {
    font-size: 10pt;
    margin-top: 4px;
  }
  
  .signature-section {
    margin-top: 20px;
  }
  
  .footer-note {
    text-align: center;
    font-size: 9pt;
    margin-top: 15px;
  }
  
  /* Print-specific styles */
  @media print {
    body * {
      visibility: hidden;
    }
    
    .contract-document, .contract-document * {
      visibility: visible;
    }
    
    .contract-document {
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      padding: 15mm;
      font-size: 10pt;
      page-break-inside: avoid;
    }
    
    .contract-title {
      font-size: 14pt;
    }
    
    .contract-section h2 {
      font-size: 11pt;
    }
    
    .parties-section {
      page-break-inside: avoid;
    }
    
    .contract-section {
      page-break-inside: avoid;
    }
    
    .signature-section {
      page-break-inside: avoid;
    }
    
    /* Hide any UI elements that shouldn't print */
    .no-print {
      display: none !important;
    }
  }
</style>`;

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-green-50 p-4">
      <div className="max-w-4xl mx-auto">
        <Card>
          <CardHeader>
            <CardTitle className="text-2xl font-bold text-center text-green-800">
              Arbeidskontrakt Generator
            </CardTitle>
            <p className="text-center text-gray-600">Ringerike Landskap AS</p>
          </CardHeader>
          <CardContent>
            {!showContract ? (
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="employeeName">Navn på arbeidstaker *</Label>
                    <Input
                      id="employeeName"
                      value={contractData.employeeName}
                      onChange={(e) => handleInputChange('employeeName', e.target.value)}
                      placeholder="Fullt navn"
                    />
                  </div>
                  <div>
                    <Label htmlFor="employeeBirthDate">Fødselsdato *</Label>
                    <Input
                      id="employeeBirthDate"
                      value={contractData.employeeBirthDate}
                      onChange={(e) => handleInputChange('employeeBirthDate', e.target.value)}
                      placeholder="dd.mm.åååå"
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="employeeAddress">Adresse *</Label>
                  <Input
                    id="employeeAddress"
                    value={contractData.employeeAddress}
                    onChange={(e) => handleInputChange('employeeAddress', e.target.value)}
                    placeholder="Gate/vei, postnummer og sted"
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="startDate">Startdato *</Label>
                    <Input
                      id="startDate"
                      type="date"
                      value={contractData.startDate}
                      onChange={(e) => handleInputChange('startDate', e.target.value)}
                    />
                  </div>
                  <div>
                    <Label htmlFor="jobTitle">Stillingstittel *</Label>
                    <Input
                      id="jobTitle"
                      value={contractData.jobTitle}
                      onChange={(e) => handleInputChange('jobTitle', e.target.value)}
                      placeholder="F.eks. Anleggsgartner, Grunnarbeider"
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="accountNumber">Kontonummer</Label>
                  <Input
                    id="accountNumber"
                    value={contractData.accountNumber}
                    onChange={(e) => handleInputChange('accountNumber', e.target.value)}
                    placeholder="XXXX.XX.XXXXX"
                  />
                </div>

                <Separator />

                <div className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="temporary"
                      checked={contractData.employmentType === 'temporary'}
                      onCheckedChange={(checked) => 
                        handleInputChange('employmentType', checked ? 'temporary' : 'permanent')
                      }
                    />
                    <Label htmlFor="temporary">Midlertidig ansettelse</Label>
                  </div>

                  {contractData.employmentType === 'temporary' && (
                    <div className="ml-6 space-y-3">
                      <div>
                        <Label htmlFor="temporaryUntil">Midlertidig til dato</Label>
                        <Input
                          id="temporaryUntil"
                          type="date"
                          value={contractData.temporaryUntil}
                          onChange={(e) => handleInputChange('temporaryUntil', e.target.value)}
                        />
                      </div>
                      <div>
                        <Label htmlFor="temporaryReason">Grunnlag for midlertidig ansettelse</Label>
                        <Textarea
                          id="temporaryReason"
                          value={contractData.temporaryReason}
                          onChange={(e) => handleInputChange('temporaryReason', e.target.value)}
                          placeholder="F.eks. Vikariat, sesongarbeid, prosjekt"
                          rows={2}
                        />
                      </div>
                    </div>
                  )}
                </div>

                <div className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="probation"
                      checked={contractData.hasProbation}
                      onCheckedChange={(checked) => handleInputChange('hasProbation', checked as boolean)}
                    />
                    <Label htmlFor="probation">Prøvetid</Label>
                  </div>

                  {contractData.hasProbation && (
                    <div className="ml-6">
                      <Label htmlFor="probationMonths">Antall måneder (maks 6)</Label>
                      <Input
                        id="probationMonths"
                        type="number"
                        min="1"
                        max="6"
                        value={contractData.probationMonths}
                        onChange={(e) => handleInputChange('probationMonths', e.target.value)}
                        placeholder="1-6"
                      />
                    </div>
                  )}
                </div>

                <Button onClick={generateContract} className="w-full bg-green-600 hover:bg-green-700">
                  Generer arbeidskontrakt
                </Button>
              </div>
            ) : (
              <div className="space-y-4">
                <div className="flex justify-between items-center no-print">
                  <h3 className="text-lg font-semibold">Generert arbeidskontrakt</h3>
                  <div className="flex gap-2">
                    <Button onClick={handlePrint} className="bg-blue-600 hover:bg-blue-700">
                      <Printer className="w-4 h-4 mr-2" />
                      Skriv ut
                    </Button>
                    <Button onClick={() => setShowContract(false)} variant="outline">
                      Tilbake til skjema
                    </Button>
                  </div>
                </div>
                
                <div 
                  className="contract-display border rounded-lg bg-white"
                  style={{ 
                    maxHeight: '70vh', 
                    overflow: 'auto'
                  }}
                  dangerouslySetInnerHTML={{ __html: contractContent }}
                />
                
                <div className="text-sm text-gray-600 bg-blue-50 p-3 rounded no-print">
                  <strong>Juridisk korrekt:</strong> Denne kontrakten oppfyller alle krav i arbeidsmiljøloven § 14-6 
                  og er formatert for profesjonell utskrift på én A4-side. Kontrakten inkluderer alle nødvendige 
                  elementer som minimumslønnsvilkår, oppsigelsesfrister, og pensjonsordninger.
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default ContractGenerator;
